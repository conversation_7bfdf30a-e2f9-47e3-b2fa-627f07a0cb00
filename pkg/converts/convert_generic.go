package converts

type (
	Boolean interface {
		~bool
	}
	Numeric interface {
		~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~uintptr | ~float32 | ~float64
	}
	Complex interface {
		~complex64 | ~complex128
	}
	Textual interface {
		~string | ~[]byte | ~[]rune
	}
)

type generic struct{ val any }

func newGeneric(val any) generic { return generic{val: val} }

func (g generic) isInt() bool {
	switch g.val.(type) {
	case int, int8, int16, int32, int64:
		return true
	}
	return false
}

func (g generic) isUint() bool {
	switch g.val.(type) {
	case uint, uint8, uint16, uint32, uint64, uintptr:
		return true
	}
	return false
}

func (g generic) isFloat() bool {
	switch g.val.(type) {
	case float32, float64:
		return true
	}
	return false
}

func (g generic) isBoolean() bool {
	switch g.val.(type) {
	case bool:
		return true
	}
	return false
}

func (g generic) isNumeric() bool {
	return g.isInt() || g.isUint() || g.isFloat()
}

func (g generic) isComplex() bool {
	switch g.val.(type) {
	case complex64, complex128:
		return true
	}
	return false
}

func (g generic) isTextual() bool {
	switch g.val.(type) {
	case string, []byte, []rune:
		return true
	}
	return false
}

func (g generic) bitSize() int {
	switch g.val.(type) {
	case int8, uint8:
		return 8
	case int16, uint16:
		return 16
	case int32, uint32, float32:
		return 32
	case int64, uint64, float64, complex64:
		return 64
	case complex128:
		return 128
	default:
		return 0
	}
}

func ToBoolean[T Boolean](i any) (T, error) {
	var zero T
	g := newGeneric(i)
	switch {
	case g.isBoolean():
		if val, ok := g.val.(T); ok {
			return val, nil
		}
		return T(g.val.(bool)), nil
	case g.isNumeric():
		return T(g.val != any(0)), nil
	case g.isComplex():
		return T(g.val != any(0+0i)), nil
	case g.isTextual():
		return T(g.val != any("")), nil
	default:
		return zero, nil
	}
}

func ToNumeric[T Numeric](v any) (T, error) {
	var zero T
	return zero, nil
}

func ToComplex[T Complex](v any) (T, error) {
	var zero T
	return zero, nil
}

func ToTextual[T Textual](v any) (T, error) {
	var zero T
	return zero, nil
}
